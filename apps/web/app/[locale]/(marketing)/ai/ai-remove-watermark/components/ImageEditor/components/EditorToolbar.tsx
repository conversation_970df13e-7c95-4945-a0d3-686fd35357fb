'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Button } from '../ui/button'
import { Separator } from '../ui/separator'
import { Slider } from '../ui/slider'
import {
  Undo,
  Redo,
  Trash2,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  UnfoldHorizontal,
  Scissors,
  Palette,
  Focus,
  Wand2,
  ChevronDown,
  Settings,
} from 'lucide-react'
import { BackgroundSelector } from './BackgroundSelector'
import { ZoomControls } from './ZoomControls'
import { createSolidColorCanvas } from '../lib/image-utils'

interface EditorToolbarProps {
  // Edit operations
  canUndo: boolean
  canRedo: boolean
  onUndo: () => void
  onRedo: () => void
  onClearAll: () => void

  // View controls
  zoom: number
  onZoomChange: (zoom: number) => void
  onPanChange: (pan: { x: number; y: number }) => void
  onCompareStart: () => void
  onCompareEnd: () => void
  showCompare: boolean

  // Background operations
  onRemoveBackground?: () => void
  isBackgroundProcessing: boolean
  backgroundRemovedImageUrl?: string | null
  onReplaceBackground?: (backgroundUrl: string) => void
  onBlurBackground?: (intensity: number) => void
  isBackgroundBlurProcessing: boolean
  backgroundBlurredImageUrl?: string | null
  blurIntensity: number
  onBlurIntensityChange: (intensity: number) => void
  originalImageUrl: string

  // Object operations
  onRemoveObjects: () => void
  isProcessing: boolean

  // General state
  disabled: boolean
}

export const EditorToolbar: React.FC<EditorToolbarProps> = ({
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onClearAll,
  zoom,
  onZoomChange,
  onPanChange,
  onCompareStart,
  onCompareEnd,
  showCompare,
  onRemoveBackground,
  isBackgroundProcessing,
  backgroundRemovedImageUrl,
  onReplaceBackground,
  onBlurBackground,
  isBackgroundBlurProcessing,
  backgroundBlurredImageUrl,
  blurIntensity,
  onBlurIntensityChange,
  originalImageUrl,
  onRemoveObjects,
  isProcessing,
  disabled,
}) => {
  const [showBackgroundSelector, setShowBackgroundSelector] = useState(false)
  const [showBlurSelector, setShowBlurSelector] = useState(false)
  const backgroundSelectorRef = useRef<HTMLDivElement>(null)
  const blurSelectorRef = useRef<HTMLDivElement>(null)

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        backgroundSelectorRef.current &&
        !backgroundSelectorRef.current.contains(event.target as Node)
      ) {
        setShowBackgroundSelector(false)
      }
      if (
        blurSelectorRef.current &&
        !blurSelectorRef.current.contains(event.target as Node)
      ) {
        setShowBlurSelector(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const isAnyProcessing =
    disabled ||
    isProcessing ||
    isBackgroundProcessing ||
    isBackgroundBlurProcessing

  // Handle color selection
  const handleColorSelect = (color: string) => {
    if (onReplaceBackground) {
      if (color === 'transparent') {
        onReplaceBackground(backgroundRemovedImageUrl || '')
      } else {
        // Create solid color background using utility function
        const dataURL = createSolidColorCanvas(800, 600, color)
        onReplaceBackground(dataURL)
      }
    }
    setShowBackgroundSelector(false)
  }

  // Handle image selection
  const handleImageSelect = async (imageUrl: string) => {
    if (onReplaceBackground) {
      await onReplaceBackground(imageUrl)
    }
    setShowBackgroundSelector(false)
  }

  return (
    <div className="flex items-center justify-between px-4 py-3 bg-white border-b border-gray-200 shadow-sm">
      {/* Left side - Edit Operations */}
      <div className="flex items-center gap-1">
        <div className="flex items-center gap-1 px-2 py-1 bg-gray-50 rounded-lg">
          <Button
            variant="ghost"
            size="sm"
            onClick={onUndo}
            disabled={isAnyProcessing || !canUndo}
            className="h-8 w-8 p-0 hover:bg-gray-200"
            title="Undo (Ctrl+Z)"
          >
            <Undo className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRedo}
            disabled={isAnyProcessing || !canRedo}
            className="h-8 w-8 p-0 hover:bg-gray-200"
            title="Redo (Ctrl+Y)"
          >
            <Redo className="w-4 h-4" />
          </Button>
          <Separator orientation="vertical" className="h-4 mx-1" />
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearAll}
            disabled={isAnyProcessing}
            className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-600"
            title="Clear All"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-2" />

        {/* View Controls */}
        <div className="flex items-center gap-1">
          <ZoomControls
            zoom={zoom}
            onZoomChange={onZoomChange}
            onPanChange={onPanChange}
            disabled={isAnyProcessing}
          />

          {showCompare && (
            <Button
              variant="outline"
              size="sm"
              onMouseDown={onCompareStart}
              onMouseUp={onCompareEnd}
              onMouseLeave={onCompareEnd}
              onTouchStart={onCompareStart}
              onTouchEnd={onCompareEnd}
              disabled={isAnyProcessing}
              className="h-9 ml-1 border-gray-300 hover:bg-gray-50"
              title="Hold to compare with original"
            >
              <UnfoldHorizontal className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Right side - Processing Operations */}
      <div className="flex items-center gap-2">
        {/* Background Remove Group */}
        <div className="flex items-center gap-1 px-2 py-1 bg-purple-50 rounded-lg border border-purple-100">
          <Button
            onClick={onRemoveBackground}
            disabled={isAnyProcessing || !onRemoveBackground}
            size="sm"
            className="h-8 bg-gradient-to-r from-purple-600 to-pink-500 hover:opacity-80 text-white text-xs font-medium"
          >
            <Scissors className="w-3 h-3 mr-1" />
            {isBackgroundProcessing ? 'Processing...' : 'Remove BG'}
          </Button>

          {backgroundRemovedImageUrl && onReplaceBackground && (
            <div ref={backgroundSelectorRef} className="relative">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setShowBackgroundSelector(!showBackgroundSelector)
                }
                disabled={isAnyProcessing}
                className="h-8 border-purple-300 text-purple-700 hover:bg-purple-50 text-xs"
                title="Replace background"
              >
                <Palette className="w-3 h-3 mr-1" />
                Replace
                <ChevronDown className="w-3 h-3 ml-1" />
              </Button>

              <BackgroundSelector
                isOpen={showBackgroundSelector}
                onClose={() => setShowBackgroundSelector(false)}
                onSelectColor={handleColorSelect}
                onSelectImage={handleImageSelect}
                originalImageUrl={originalImageUrl}
                disabled={isAnyProcessing}
              />
            </div>
          )}
        </div>

        {/* Background Blur Group */}
        <div
          ref={blurSelectorRef}
          className="relative flex items-center gap-1 px-2 py-1 bg-orange-50 rounded-lg border border-orange-100"
        >
          <Button
            onClick={() => {
              if (onBlurBackground) {
                onBlurBackground(blurIntensity)
              }
            }}
            disabled={isAnyProcessing || !onBlurBackground}
            size="sm"
            className="h-8 bg-gradient-to-r from-orange-600 to-orange-400 hover:opacity-80 text-white text-xs font-medium"
          >
            <Focus className="w-3 h-3 mr-1" />
            {isBackgroundBlurProcessing ? 'Processing...' : 'Blur BG'}
          </Button>

          {backgroundBlurredImageUrl && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowBlurSelector(!showBlurSelector)}
              disabled={isAnyProcessing}
              className="h-8 w-8 p-0 border-orange-300 text-orange-700 hover:bg-orange-50"
              title="Adjust blur intensity"
            >
              <ChevronDown className="w-3 h-3" />
            </Button>
          )}

          {/* Blur intensity dropdown */}
          {showBlurSelector && backgroundBlurredImageUrl && (
            <div className="absolute top-full right-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
              <div className="p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-3">
                  Blur Intensity
                </h4>
                <div className="space-y-3">
                  <Slider
                    value={[blurIntensity]}
                    onValueChange={(value) => onBlurIntensityChange(value[0])}
                    max={100}
                    min={5}
                    step={5}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Light (5%)</span>
                    <span className="font-medium">{blurIntensity}%</span>
                    <span>Heavy (100%)</span>
                  </div>
                  <div className="flex gap-2 mt-3">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onBlurIntensityChange(20)}
                      className="flex-1 text-xs hover:bg-white hover:opacity-60"
                    >
                      Default (20%)
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => {
                        if (onBlurBackground) {
                          onBlurBackground(blurIntensity)
                        }
                        setShowBlurSelector(false)
                      }}
                      disabled={!onBlurBackground}
                      className="flex-1 bg-orange-600 hover:bg-orange-700 text-white text-xs"
                    >
                      Apply Blur
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Object Operations */}
        <Button
          onClick={onRemoveObjects}
          disabled={isAnyProcessing}
          size="sm"
          className="h-8 bg-gradient-to-r from-blue-600 to-purple-500 hover:opacity-80 text-white text-xs font-medium"
        >
          <Wand2 className="w-3 h-3 mr-1" />
          {isProcessing ? 'Processing...' : 'Remove Objects'}
        </Button>
      </div>
    </div>
  )
}
