/* 将全局滚动条样式限制在特定容器内 */
.ai-remove-watermark::-webkit-scrollbar {
  width: 8px;
}

.ai-remove-watermark::-webkit-scrollbar-track {
  background: white !important;
}

.ai-remove-watermark::-webkit-scrollbar-thumb {
  background: rgba(
    15,
    23,
    42,
    0.1
  ) !important; /* from-purple-500 to-pink-500 */
  border-radius: 4px;
}

.ai-remove-watermark::-webkit-scrollbar-thumb:hover {
  background: rgba(
    15,
    23,
    42,
    0.3
  ) !important; /* hover: from-purple-600 to-pink-400 */
}
